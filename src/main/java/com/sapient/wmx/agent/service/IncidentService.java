package com.sapient.wmx.agent.service;

import com.sapient.wmx.agent.model.IncidentHistory;
import com.sapient.wmx.agent.model.Incident;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.JsonMappingException;

import java.util.List;
import java.util.ArrayList;

@Service
public class IncidentService {

    private static final Logger logger = LoggerFactory.getLogger(IncidentService.class);
    private final ChatClient chatClient;

    public IncidentService(ChatClient.Builder chatClient) {
        this.chatClient = chatClient.build();
    }

    public IncidentHistory getIncidents(String query) {
        int maxRetries = 3;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("Attempting to get incidents, attempt: {}", attempt);
                
                String prompt = buildPrompt(query, attempt);
                
                IncidentHistory result = chatClient.prompt()
                        .user(prompt)
                        .call()
                        .entity(IncidentHistory.class);
                
                logger.info("Successfully retrieved {} incidents", 
                    result.incidents() != null ? result.incidents().size() : 0);
                return result;
                
            } catch (RuntimeException e) {
                logger.warn("Attempt {} failed: {}", attempt, e.getMessage());
                
                if (attempt == maxRetries) {
                    logger.error("All attempts failed, returning fallback data", e);
                    return createFallbackIncidentHistory();
                }
                
                // Wait a bit before retrying
                try {
                    Thread.sleep(1000 * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return createFallbackIncidentHistory();
    }

    private String buildPrompt(String query, int attempt) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("Generate a list of 10 production incidents for ServiceNow. ");
        prompt.append("IMPORTANT: Return ONLY valid JSON. ");
        prompt.append("Ensure all string values are properly quoted with double quotes. ");
        prompt.append("Do not include any markdown formatting or code blocks. ");
        
        if (attempt > 1) {
            prompt.append("Previous attempt failed due to malformed JSON. ");
            prompt.append("Pay special attention to proper JSON escaping. ");
        }
        
        prompt.append("Format: {\"incidents\": [{\"sysId\": \"INC001\", \"number\": \"INC0000001\", ");
        prompt.append("\"shortDescription\": \"Brief description\", \"description\": \"Detailed description\", ");
        prompt.append("\"state\": \"Open\", \"priority\": \"High\", \"assignedTo\": \"John Doe\", ");
        prompt.append("\"category\": \"Software\", \"subcategory\": \"Application\", ");
        prompt.append("\"createdOn\": \"2024-01-01T10:00:00Z\", \"updatedOn\": \"2024-01-01T11:00:00Z\"}]}");
        
        return prompt.toString();
    }

    private IncidentHistory createFallbackIncidentHistory() {
        List<Incident> fallbackIncidents = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            fallbackIncidents.add(new Incident(
                "INC00" + i,
                "INC000000" + i,
                "Sample incident " + i,
                "This is a sample incident description " + i,
                "Open",
                "Medium",
                "System Admin",
                "Software",
                "Application",
                "2024-01-0" + i + "T10:00:00Z",
                "2024-01-0" + i + "T11:00:00Z"
            ));
        }
        
        logger.info("Created fallback incident history with {} incidents", fallbackIncidents.size());
        return new IncidentHistory(fallbackIncidents);
    }
}
