package com.sapient.wmx.agent.chat;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.ChatClient.Builder;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
public class ChatController {

    private final ChatClient chatClient;

    public ChatController(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    @GetMapping("/chat")
    public ChatResponse chat(String message) {
        return chatClient.prompt()
                .user("Tell me interesting thing about Java")
                .call()
                .chatResponse();
    }

    @GetMapping("/stream")
    public Flux<String> stream(String message) {
        return chatClient.prompt()
                .user("Tell me interesting thing about Java 24 features")
                .stream()
                .content();
    }
}
