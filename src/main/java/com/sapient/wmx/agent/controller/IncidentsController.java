package com.sapient.wmx.agent.controller;

import com.sapient.wmx.agent.model.IncidentHistory;
import com.sapient.wmx.agent.service.IncidentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
public class IncidentsController {

    private static final Logger logger = LoggerFactory.getLogger(IncidentsController.class);
    private final IncidentService incidentService;

    public IncidentsController(IncidentService incidentService) {
        this.incidentService = incidentService;
    }

    @GetMapping("/incidents")
    public ResponseEntity<IncidentHistory> getIncidents(@RequestParam(required = false) String query) {
        try {
            IncidentHistory result = incidentService.getIncidents(query);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Unexpected error in getIncidents", e);
            return ResponseEntity.status(500).build();
        }
    }
}
